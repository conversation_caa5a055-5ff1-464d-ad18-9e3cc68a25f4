# WebRTC推流测试页面

## 📋 概述

这是一个用于测试WebRTC推流到SRS服务器的前端页面，支持实时视频推流、状态监控和多种播放格式。

## 🚀 快速开始

### 1. 部署测试页面

将 `web` 文件夹中的文件部署到Web服务器，或者直接在浏览器中打开 `webrtc-publisher.html`。

### 2. 配置服务器地址

在页面中修改以下配置：
- **服务器地址**: `https://**************:1990` (修改为您的SRS服务器IP)
- **应用名**: `live` (默认即可)
- **流名**: `livestream` (可自定义)

### 3. 开始推流

1. 点击"开始推流"按钮
2. 允许浏览器访问摄像头和麦克风
3. 等待连接建立成功
4. 查看推流统计信息

### 4. 播放测试

推流成功后，可以使用以下地址进行播放测试：

- **WebRTC播放**: `webrtc://**************/live/livestream`
- **HLS播放**: `https://**************:8088/live/livestream.m3u8`
- **HTTP-FLV播放**: `https://**************:8088/live/livestream.flv`

## 🔧 功能特性

### 推流功能
- ✅ 实时视频推流
- ✅ 音频推流
- ✅ 多种分辨率支持 (480p/720p/1080p)
- ✅ 可调节帧率 (15/25/30 FPS)
- ✅ 自动音频处理 (回声消除、噪声抑制)

### 监控功能
- ✅ 实时连接状态显示
- ✅ 视频/音频码率监控
- ✅ 丢包统计
- ✅ 推流时长计时
- ✅ 分辨率和帧率显示

### 用户界面
- ✅ 响应式设计，支持移动端
- ✅ 实时视频预览
- ✅ 直观的状态指示器
- ✅ 详细的错误提示
- ✅ 播放地址自动生成

## 📱 浏览器兼容性

| 浏览器 | 最低版本 | 推荐版本 | 备注 |
|--------|----------|----------|------|
| Chrome | 60+ | 最新版 | 最佳支持 |
| Firefox | 55+ | 最新版 | 良好支持 |
| Safari | 11+ | 最新版 | 需要HTTPS |
| Edge | 79+ | 最新版 | 基于Chromium |

## ⚠️ 使用注意事项

### HTTPS要求
- 现代浏览器要求HTTPS环境才能访问摄像头
- 本地测试可使用 `localhost` 或 `127.0.0.1`
- 生产环境必须配置有效的SSL证书

### 网络要求
- 确保客户端能访问SRS服务器的8088端口(HTTPS)
- 确保UDP 8000端口开放用于WebRTC媒体传输
- 内网环境需要正确配置candidate IP

### 设备要求
- 需要摄像头和麦克风设备
- 建议使用有线网络或稳定的WiFi
- 推荐使用性能较好的设备以确保流畅推流

## 🔍 故障排除

### 常见问题

1. **无法访问摄像头**
   - 检查浏览器权限设置
   - 确保使用HTTPS协议
   - 检查摄像头是否被其他应用占用

2. **推流连接失败**
   - 检查SRS服务器是否正常运行
   - 验证服务器地址和端口配置
   - 检查网络连接和防火墙设置

3. **推流质量差**
   - 降低视频分辨率和帧率
   - 检查网络带宽是否充足
   - 确保设备性能满足要求

4. **播放无法观看**
   - 等待几秒钟让流建立稳定
   - 检查播放器是否支持对应格式
   - 验证播放地址是否正确

### 调试方法

1. **查看浏览器控制台**
   ```
   F12 → Console → 查看错误信息
   ```

2. **检查网络请求**
   ```
   F12 → Network → 查看WHIP请求状态
   ```

3. **查看WebRTC统计**
   ```
   chrome://webrtc-internals/
   ```

## 📊 性能建议

### 推荐配置

**高质量推流**:
- 分辨率: 1280x720
- 帧率: 30 FPS
- 网络带宽: 5 Mbps+

**标准推流**:
- 分辨率: 640x480
- 帧率: 25 FPS
- 网络带宽: 2 Mbps+

**移动端推流**:
- 分辨率: 480x360
- 帧率: 15 FPS
- 网络带宽: 1 Mbps+

### 优化建议

1. **网络优化**
   - 使用有线网络连接
   - 确保网络延迟低于100ms
   - 避免网络拥塞时段

2. **设备优化**
   - 关闭不必要的应用程序
   - 确保设备散热良好
   - 使用外接摄像头获得更好画质

3. **浏览器优化**
   - 关闭其他标签页
   - 禁用不必要的扩展
   - 使用最新版本浏览器

## 📞 技术支持

如遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查SRS服务器日志
3. 参考 `docs/前端WebRTC推流接入指南.md`
4. 联系技术支持团队

---

**更新时间**: 2025-08-26  
**版本**: 1.0.0  
**兼容性**: SRS 5.0+

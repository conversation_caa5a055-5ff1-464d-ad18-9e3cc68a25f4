<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC推流测试 - SRS服务器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .video-section {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 30px;
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 16/9;
        }

        #localVideo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-overlay {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 250px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-disconnected { background: #f44336; }
        .status-connecting { background: #ff9800; }
        .status-connected { background: #4CAF50; }

        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: #da190b;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .stats-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4CAF50;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .play-urls {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
        }

        .url-item {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        .url-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .url-value {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            word-break: break-all;
            font-size: 0.9em;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4CAF50;
        }

        .alert-warning {
            background: #fff3e0;
            color: #ef6c00;
            border-left: 4px solid #ff9800;
        }

        @media (max-width: 768px) {
            .video-section {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2em;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 WebRTC推流测试</h1>
            <p>连接到SRS服务器进行实时视频推流</p>
        </div>

        <div class="main-content">
            <!-- 错误提示区域 -->
            <div id="alertContainer"></div>

            <!-- 视频和控制面板 -->
            <div class="video-section">
                <div class="video-container">
                    <video id="localVideo" autoplay muted playsinline></video>
                    <div class="video-overlay">
                        <div id="connectionStatus">
                            <span class="status-indicator status-disconnected"></span>
                            <span>未连接</span>
                        </div>
                        <div id="streamInfo" style="margin-top: 10px; font-size: 12px;">
                            <div>分辨率: <span id="resolution">-</span></div>
                            <div>帧率: <span id="framerate">-</span></div>
                        </div>
                    </div>
                </div>

                <div class="control-panel">
                    <div class="control-group">
                        <h3>🔧 推流配置</h3>
                        <div class="form-group">
                            <label>服务器地址:</label>
                            <input type="text" id="serverUrl" value="https://**************:1990">
                        </div>
                        <div class="form-group">
                            <label>应用名:</label>
                            <input type="text" id="appName" value="live">
                        </div>
                        <div class="form-group">
                            <label>流名:</label>
                            <input type="text" id="streamName" value="livestream">
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>📹 视频设置</h3>
                        <div class="form-group">
                            <label>分辨率:</label>
                            <select id="videoQuality">
                                <option value="hd">高清 (1280x720)</option>
                                <option value="sd" selected>标清 (640x480)</option>
                                <option value="mobile">移动 (480x360)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>帧率:</label>
                            <select id="frameRate">
                                <option value="15">15 FPS</option>
                                <option value="25" selected>25 FPS</option>
                                <option value="30">30 FPS</option>
                            </select>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>🎮 控制</h3>
                        <button id="startBtn" class="btn btn-primary">
                            🚀 开始推流
                        </button>
                        <button id="stopBtn" class="btn btn-danger" disabled>
                            ⏹️ 停止推流
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-section">
                <h3>📊 推流统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="videoBitrate">0</div>
                        <div class="stat-label">视频码率 (kbps)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="audioBitrate">0</div>
                        <div class="stat-label">音频码率 (kbps)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="packetsLost">0</div>
                        <div class="stat-label">丢包数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="duration">00:00</div>
                        <div class="stat-label">推流时长</div>
                    </div>
                </div>
            </div>

            <!-- 播放地址 -->
            <div class="play-urls">
                <h3>🎬 播放地址</h3>
                <div class="url-item">
                    <div class="url-label">WebRTC播放:</div>
                    <div class="url-value" id="webrtcPlayUrl">webrtc://**************/live/livestream</div>
                </div>
                <div class="url-item">
                    <div class="url-label">HLS播放:</div>
                    <div class="url-value" id="hlsPlayUrl">https://**************:8088/live/livestream.m3u8</div>
                </div>
                <div class="url-item">
                    <div class="url-label">HTTP-FLV播放:</div>
                    <div class="url-value" id="flvPlayUrl">https://**************:8088/live/livestream.flv</div>
                </div>
            </div>
        </div>
    </div>

    <script src="webrtc-publisher.js"></script>
</body>
</html>

// WebRTC推流器类
class WebRTCPublisher {
    constructor() {
        this.pc = null;
        this.localStream = null;
        this.isPublishing = false;
        this.startTime = null;
        this.statsInterval = null;
        this.lastStats = null;
        
        // 绑定UI元素
        this.initializeElements();
        this.bindEvents();
        
        // 检查浏览器支持
        this.checkBrowserSupport();
    }

    initializeElements() {
        this.elements = {
            localVideo: document.getElementById('localVideo'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            serverUrl: document.getElementById('serverUrl'),
            appName: document.getElementById('appName'),
            streamName: document.getElementById('streamName'),
            videoQuality: document.getElementById('videoQuality'),
            frameRate: document.getElementById('frameRate'),
            connectionStatus: document.getElementById('connectionStatus'),
            resolution: document.getElementById('resolution'),
            framerate: document.getElementById('framerate'),
            videoBitrate: document.getElementById('videoBitrate'),
            audioBitrate: document.getElementById('audioBitrate'),
            packetsLost: document.getElementById('packetsLost'),
            duration: document.getElementById('duration'),
            alertContainer: document.getElementById('alertContainer'),
            webrtcPlayUrl: document.getElementById('webrtcPlayUrl'),
            hlsPlayUrl: document.getElementById('hlsPlayUrl'),
            flvPlayUrl: document.getElementById('flvPlayUrl')
        };
    }

    bindEvents() {
        this.elements.startBtn.addEventListener('click', () => this.startPublish());
        this.elements.stopBtn.addEventListener('click', () => this.stopPublish());
        
        // 监听配置变化，更新播放地址
        [this.elements.serverUrl, this.elements.appName, this.elements.streamName].forEach(element => {
            element.addEventListener('input', () => this.updatePlayUrls());
        });
        
        // 初始化播放地址
        this.updatePlayUrls();
    }

    checkBrowserSupport() {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showAlert('error', '您的浏览器不支持WebRTC，请使用Chrome、Firefox或Safari的最新版本');
            return false;
        }
        
        if (!window.RTCPeerConnection) {
            this.showAlert('error', '您的浏览器不支持RTCPeerConnection');
            return false;
        }
        
        return true;
    }

    getVideoConstraints() {
        const quality = this.elements.videoQuality.value;
        const frameRate = parseInt(this.elements.frameRate.value);
        
        const constraints = {
            hd: { width: 1280, height: 720 },
            sd: { width: 640, height: 480 },
            mobile: { width: 480, height: 360 }
        };
        
        return {
            ...constraints[quality],
            frameRate: frameRate
        };
    }

    async getMediaStream() {
        const videoConstraints = this.getVideoConstraints();
        
        const constraints = {
            video: videoConstraints,
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        };

        try {
            this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.elements.localVideo.srcObject = this.localStream;
            
            // 更新视频信息显示
            const videoTrack = this.localStream.getVideoTracks()[0];
            const settings = videoTrack.getSettings();
            this.elements.resolution.textContent = `${settings.width}x${settings.height}`;
            this.elements.framerate.textContent = `${settings.frameRate || 'Auto'} FPS`;
            
            return this.localStream;
        } catch (error) {
            throw new Error(this.getMediaErrorMessage(error));
        }
    }

    getMediaErrorMessage(error) {
        switch(error.name) {
            case 'NotAllowedError':
                return '用户拒绝了摄像头/麦克风权限，请允许访问后重试';
            case 'NotFoundError':
                return '未找到摄像头或麦克风设备';
            case 'NotReadableError':
                return '摄像头或麦克风被其他应用占用';
            case 'OverconstrainedError':
                return '摄像头不支持指定的分辨率或帧率';
            default:
                return `媒体设备错误: ${error.message}`;
        }
    }

    createPeerConnection() {
        const config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ],
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require'
        };

        this.pc = new RTCPeerConnection(config);

        // 添加媒体轨道
        this.localStream.getTracks().forEach(track => {
            this.pc.addTrack(track, this.localStream);
        });

        // 监听连接状态变化
        this.pc.onconnectionstatechange = () => {
            this.updateConnectionStatus(this.pc.connectionState);
        };

        // 监听ICE连接状态
        this.pc.oniceconnectionstatechange = () => {
            console.log('ICE连接状态:', this.pc.iceConnectionState);
        };

        return this.pc;
    }

    async startPublish() {
        try {
            this.showAlert('warning', '正在启动推流，请稍候...');
            this.setButtonState(true);
            
            // 1. 获取媒体流
            await this.getMediaStream();
            
            // 2. 创建PeerConnection
            this.createPeerConnection();
            
            // 3. 创建Offer
            const offer = await this.pc.createOffer({
                offerToReceiveAudio: false,
                offerToReceiveVideo: false
            });
            await this.pc.setLocalDescription(offer);
            
            // 4. 发送WHIP请求
            const answer = await this.sendWhipRequest(offer);
            
            // 5. 设置远程描述
            const answerDesc = new RTCSessionDescription({
                type: 'answer',
                sdp: answer
            });
            await this.pc.setRemoteDescription(answerDesc);
            
            // 6. 启动统计监控
            this.startStatsMonitoring();
            
            this.isPublishing = true;
            this.startTime = Date.now();
            this.showAlert('success', '推流启动成功！您可以使用下方的播放地址观看直播');
            
        } catch (error) {
            console.error('推流失败:', error);
            this.showAlert('error', `推流失败: ${error.message}`);
            this.stopPublish();
        }
    }

    async sendWhipRequest(offer) {
        const serverUrl = this.elements.serverUrl.value.trim();
        const appName = this.elements.appName.value.trim();
        const streamName = this.elements.streamName.value.trim();
        
        const whipUrl = `${serverUrl}/rtc/v1/whip/?app=${appName}&stream=${streamName}`;
        
        const response = await fetch(whipUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/sdp',
                'Accept': 'application/sdp'
            },
            body: offer.sdp
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`WHIP请求失败 (${response.status}): ${errorText}`);
        }

        return await response.text();
    }

    stopPublish() {
        this.isPublishing = false;
        
        // 停止统计监控
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
            this.statsInterval = null;
        }
        
        // 关闭媒体流
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
            this.elements.localVideo.srcObject = null;
        }

        // 关闭PeerConnection
        if (this.pc) {
            this.pc.close();
            this.pc = null;
        }

        // 重置UI状态
        this.setButtonState(false);
        this.updateConnectionStatus('closed');
        this.resetStats();
        this.clearAlerts();
        
        console.log('推流已停止');
    }

    setButtonState(isPublishing) {
        this.elements.startBtn.disabled = isPublishing;
        this.elements.stopBtn.disabled = !isPublishing;
        
        if (isPublishing) {
            this.elements.startBtn.innerHTML = '<span class="loading"></span>推流中...';
        } else {
            this.elements.startBtn.innerHTML = '🚀 开始推流';
        }
    }

    updateConnectionStatus(state) {
        const statusElement = this.elements.connectionStatus;
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('span:last-child');
        
        // 移除所有状态类
        indicator.className = 'status-indicator';
        
        switch(state) {
            case 'connecting':
                indicator.classList.add('status-connecting');
                text.textContent = '连接中...';
                break;
            case 'connected':
                indicator.classList.add('status-connected');
                text.textContent = '已连接';
                break;
            case 'disconnected':
                indicator.classList.add('status-connecting');
                text.textContent = '连接断开';
                break;
            case 'failed':
                indicator.classList.add('status-disconnected');
                text.textContent = '连接失败';
                break;
            case 'closed':
            default:
                indicator.classList.add('status-disconnected');
                text.textContent = '未连接';
                break;
        }
    }

    startStatsMonitoring() {
        this.statsInterval = setInterval(async () => {
            if (this.pc && this.isPublishing) {
                await this.updateStats();
                this.updateDuration();
            }
        }, 1000);
    }

    async updateStats() {
        try {
            const stats = await this.pc.getStats();
            const videoStats = { bitrate: 0, packetsLost: 0 };
            const audioStats = { bitrate: 0, packetsLost: 0 };
            
            stats.forEach(report => {
                if (report.type === 'outbound-rtp') {
                    const currentTime = Date.now();
                    
                    if (this.lastStats && this.lastStats[report.id]) {
                        const timeDiff = (currentTime - this.lastStats.timestamp) / 1000;
                        const bytesDiff = report.bytesSent - this.lastStats[report.id].bytesSent;
                        const bitrate = Math.round((bytesDiff * 8) / timeDiff / 1000); // kbps
                        
                        if (report.mediaType === 'video') {
                            videoStats.bitrate = bitrate;
                            videoStats.packetsLost = report.packetsLost || 0;
                        } else if (report.mediaType === 'audio') {
                            audioStats.bitrate = bitrate;
                            audioStats.packetsLost = report.packetsLost || 0;
                        }
                    }
                    
                    if (!this.lastStats) this.lastStats = { timestamp: currentTime };
                    this.lastStats[report.id] = {
                        bytesSent: report.bytesSent,
                        packetsLost: report.packetsLost || 0
                    };
                    this.lastStats.timestamp = currentTime;
                }
            });
            
            // 更新UI显示
            this.elements.videoBitrate.textContent = videoStats.bitrate;
            this.elements.audioBitrate.textContent = audioStats.bitrate;
            this.elements.packetsLost.textContent = videoStats.packetsLost + audioStats.packetsLost;
            
        } catch (error) {
            console.error('获取统计信息失败:', error);
        }
    }

    updateDuration() {
        if (this.startTime) {
            const duration = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            this.elements.duration.textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    resetStats() {
        this.elements.videoBitrate.textContent = '0';
        this.elements.audioBitrate.textContent = '0';
        this.elements.packetsLost.textContent = '0';
        this.elements.duration.textContent = '00:00';
        this.elements.resolution.textContent = '-';
        this.elements.framerate.textContent = '-';
        this.lastStats = null;
        this.startTime = null;
    }

    updatePlayUrls() {
        const serverUrl = this.elements.serverUrl.value.trim();
        const appName = this.elements.appName.value.trim();
        const streamName = this.elements.streamName.value.trim();
        
        // 提取服务器IP
        const serverIP = serverUrl.replace(/https?:\/\//, '').split(':')[0];
        
        this.elements.webrtcPlayUrl.textContent = `webrtc://${serverIP}/${appName}/${streamName}`;
        this.elements.hlsPlayUrl.textContent = `https://${serverIP}:8088/${appName}/${streamName}.m3u8`;
        this.elements.flvPlayUrl.textContent = `https://${serverIP}:8088/${appName}/${streamName}.flv`;
    }

    showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        this.elements.alertContainer.innerHTML = '';
        this.elements.alertContainer.appendChild(alertDiv);
        
        // 自动隐藏成功和警告消息
        if (type === 'success' || type === 'warning') {
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }

    clearAlerts() {
        this.elements.alertContainer.innerHTML = '';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.publisher = new WebRTCPublisher();
    
    // 添加页面卸载时的清理
    window.addEventListener('beforeunload', () => {
        if (window.publisher && window.publisher.isPublishing) {
            window.publisher.stopPublish();
        }
    });
});

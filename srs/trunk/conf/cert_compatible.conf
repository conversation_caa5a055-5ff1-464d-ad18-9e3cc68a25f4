[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C=CN
ST=Beijing
L=Beijing
O=SRS Server
OU=WebRTC
CN=**************

[v3_req]
basicConstraints = CA:FALSE
keyUsage = critical, digitalSignature, keyEncipherment, dataEncipherment, keyAgreement
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names
authorityKeyIdentifier = keyid,issuer:always
subjectKeyIdentifier = hash

[alt_names]
IP.1 = **************
IP.2 = 127.0.0.1
DNS.1 = localhost
DNS.2 = gpuserver

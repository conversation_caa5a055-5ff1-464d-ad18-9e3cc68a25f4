#!/bin/bash

# 生成适用于内网IP的自签名证书
# 用于SRS WebRTC HTTPS服务

SERVER_IP="**************"
CERT_DIR="/data/threeServices/srs/trunk/conf"
KEY_FILE="$CERT_DIR/server_internal.key"
CERT_FILE="$CERT_DIR/server_internal.crt"

echo "正在为IP地址 $SERVER_IP 生成自签名证书..."

# 生成私钥
openssl genrsa -out "$KEY_FILE" 2048

# 创建证书配置文件
cat > "$CERT_DIR/cert.conf" << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C=CN
ST=Beijing
L=Beijing
O=SRS Server
OU=WebRTC
CN=$SERVER_IP

[v3_req]
basicConstraints = CA:FALSE
keyUsage = critical, digitalSignature, keyEncipherment, dataEncipherment, keyAgreement
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names
authorityKeyIdentifier = keyid,issuer:always
subjectKeyIdentifier = hash

[alt_names]
IP.1 = $SERVER_IP
IP.2 = 127.0.0.1
DNS.1 = localhost
EOF

# 生成证书
openssl req -new -x509 -key "$KEY_FILE" -out "$CERT_FILE" -days 3650 \
    -config "$CERT_DIR/cert.conf" -extensions v3_req

echo "证书生成完成！"
echo "私钥文件: $KEY_FILE"
echo "证书文件: $CERT_FILE"
echo ""
echo "请在SRS配置文件中使用这些证书文件。"

# SRS WebRTC HTTPS生产环境配置
# 适用于内网IP: **************
# 支持WebRTC推流和HTTPS访问

listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;

# HTTP服务器配置（同时支持HTTP和HTTPS）
http_server {
    enabled         on;
    listen          8080;                    # HTTP端口
    dir             ./objs/nginx/html;
    
    # HTTPS配置
    https {
        enabled     on;
        listen      8088;                    # HTTPS端口
        key         ./conf/server_compatible.key;    # 使用兼容的私钥
        cert        ./conf/server_compatible.crt;    # 使用兼容的证书
    }
}

# HTTP API配置（同时支持HTTP和HTTPS）
http_api {
    enabled         on;
    listen          1985;                    # HTTP API端口
    
    # HTTPS API配置
    https {
        enabled     on;
        listen      1990;                    # HTTPS API端口
        key         ./conf/server_compatible.key;
        cert        ./conf/server_compatible.crt;
    }
}

# 统计信息配置
stats {
    network         0;
}

# WebRTC服务器配置
rtc_server {
    enabled         on;
    listen          8000;                    # WebRTC UDP端口
    candidate       **************;         # 明确指定内网IP
    protocol        udp;                     # 使用UDP协议
    use_auto_detect_network_ip off;         # 关闭自动检测
    api_as_candidates off;                  # 不使用API IP作为候选
    ip_family       ipv4;                   # 使用IPv4
    ecdsa           off;                    # 使用RSA证书，不是ECDSA
    encrypt         on;                     # 启用SRTP加密
}

# 虚拟主机配置
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled     on;                     # 启用WebRTC
        rtc_to_rtmp on;                    # 允许WebRTC推流转RTMP
        rtmp_to_rtc off;                   # 不需要RTMP转WebRTC（节省资源）
        nack        on;                    # 启用NACK重传
    }
    
    # HTTP重混流配置（用于HTTP-FLV播放）
    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }
    
    # HLS配置（可选）
    hls {
        enabled     on;
        hls_fragment 2;                    # 2秒分片
        hls_window   10;                   # 保留10个分片
    }
}

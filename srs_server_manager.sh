#!/bin/bash

# SRS服务器管理脚本
# 用于启动、停止、重启和检查SRS服务器状态

SRS_HOME="/data/threeServices/srs/trunk"
SRS_BINARY="$SRS_HOME/objs/srs"
SRS_CONFIG="$SRS_HOME/conf/webrtc_https_production.conf"
SRS_PID_FILE="$SRS_HOME/objs/srs.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查SRS是否正在运行
is_srs_running() {
    if [ -f "$SRS_PID_FILE" ]; then
        local pid=$(cat "$SRS_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 获取SRS进程ID
get_srs_pid() {
    if [ -f "$SRS_PID_FILE" ]; then
        cat "$SRS_PID_FILE"
    else
        echo ""
    fi
}

# 启动SRS服务器
start_srs() {
    print_message $BLUE "🚀 启动SRS服务器..."
    
    if is_srs_running; then
        print_message $YELLOW "⚠️  SRS服务器已在运行中 (PID: $(get_srs_pid))"
        return 1
    fi
    
    # 检查配置文件
    if [ ! -f "$SRS_CONFIG" ]; then
        print_message $RED "❌ 配置文件不存在: $SRS_CONFIG"
        return 1
    fi
    
    # 检查可执行文件
    if [ ! -f "$SRS_BINARY" ]; then
        print_message $RED "❌ SRS可执行文件不存在: $SRS_BINARY"
        print_message $YELLOW "请先编译SRS: cd $SRS_HOME && make"
        return 1
    fi
    
    # 启动SRS
    cd "$SRS_HOME"
    nohup "$SRS_BINARY" -c "$SRS_CONFIG" > srs.log 2>&1 &
    
    # 等待启动
    sleep 3
    
    if is_srs_running; then
        print_message $GREEN "✅ SRS服务器启动成功 (PID: $(get_srs_pid))"
        print_message $BLUE "📋 访问地址:"
        print_message $NC "   - 管理控制台: https://**************:8088/"
        print_message $NC "   - WebRTC推流: https://**************:8088/players/whip.html"
        print_message $NC "   - API接口: https://**************:1990/api/v1/versions"
        return 0
    else
        print_message $RED "❌ SRS服务器启动失败"
        print_message $YELLOW "请查看日志: tail -f $SRS_HOME/srs.log"
        return 1
    fi
}

# 停止SRS服务器
stop_srs() {
    print_message $BLUE "⏹️  停止SRS服务器..."
    
    if ! is_srs_running; then
        print_message $YELLOW "⚠️  SRS服务器未运行"
        return 1
    fi
    
    local pid=$(get_srs_pid)
    print_message $YELLOW "正在停止SRS进程 (PID: $pid)..."
    
    # 优雅停止
    kill -TERM "$pid" 2>/dev/null
    
    # 等待进程结束
    local count=0
    while [ $count -lt 10 ]; do
        if ! is_srs_running; then
            print_message $GREEN "✅ SRS服务器已停止"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # 强制停止
    print_message $YELLOW "正在强制停止SRS进程..."
    kill -KILL "$pid" 2>/dev/null
    
    if ! is_srs_running; then
        print_message $GREEN "✅ SRS服务器已强制停止"
        return 0
    else
        print_message $RED "❌ 无法停止SRS服务器"
        return 1
    fi
}

# 重启SRS服务器
restart_srs() {
    print_message $BLUE "🔄 重启SRS服务器..."
    stop_srs
    sleep 2
    start_srs
}

# 检查SRS服务器状态
status_srs() {
    print_message $BLUE "📊 SRS服务器状态检查"
    print_message $NC "=========================="
    
    if is_srs_running; then
        local pid=$(get_srs_pid)
        print_message $GREEN "✅ SRS服务器正在运行 (PID: $pid)"
        
        # 检查端口监听
        print_message $BLUE "\n🌐 端口监听状态:"
        local ports=(1935 8080 8088 1985 1990 8000)
        for port in "${ports[@]}"; do
            if ss -tuln 2>/dev/null | grep -q ":$port " || netstat -tuln 2>/dev/null | grep -q ":$port "; then
                print_message $GREEN "  ✅ 端口 $port: 正在监听"
            else
                print_message $RED "  ❌ 端口 $port: 未监听"
            fi
        done
        
        # 检查服务响应
        print_message $BLUE "\n🔐 服务响应测试:"
        if curl -k -s --connect-timeout 5 https://localhost:8088/ | grep -q "html\|SRS" 2>/dev/null; then
            print_message $GREEN "  ✅ HTTPS服务: 正常"
        else
            print_message $RED "  ❌ HTTPS服务: 异常"
        fi

        if curl -s --connect-timeout 5 http://localhost:1985/api/v1/versions | grep -q "version\|SRS" 2>/dev/null; then
            print_message $GREEN "  ✅ API服务: 正常"
        else
            print_message $RED "  ❌ API服务: 异常"
        fi
        
        # 显示资源使用情况
        print_message $BLUE "\n📈 资源使用情况:"
        local cpu_mem=$(ps -p "$pid" -o %cpu,%mem --no-headers 2>/dev/null)
        if [ -n "$cpu_mem" ]; then
            print_message $NC "  CPU: $(echo $cpu_mem | awk '{print $1}')%"
            print_message $NC "  内存: $(echo $cpu_mem | awk '{print $2}')%"
        fi
        
    else
        print_message $RED "❌ SRS服务器未运行"
    fi
}

# 查看SRS日志
logs_srs() {
    local log_file="$SRS_HOME/srs.log"
    if [ -f "$log_file" ]; then
        print_message $BLUE "📋 SRS服务器日志 (最后50行):"
        print_message $NC "================================"
        tail -n 50 "$log_file"
    else
        print_message $YELLOW "⚠️  日志文件不存在: $log_file"
    fi
}

# 显示帮助信息
show_help() {
    print_message $BLUE "SRS服务器管理脚本"
    print_message $NC "=================="
    print_message $NC ""
    print_message $NC "用法: $0 {start|stop|restart|status|logs|help}"
    print_message $NC ""
    print_message $NC "命令说明:"
    print_message $NC "  start   - 启动SRS服务器"
    print_message $NC "  stop    - 停止SRS服务器"
    print_message $NC "  restart - 重启SRS服务器"
    print_message $NC "  status  - 检查服务器状态"
    print_message $NC "  logs    - 查看服务器日志"
    print_message $NC "  help    - 显示此帮助信息"
    print_message $NC ""
    print_message $NC "配置文件: $SRS_CONFIG"
    print_message $NC "日志文件: $SRS_HOME/srs.log"
}

# 主函数
main() {
    case "$1" in
        start)
            start_srs
            ;;
        stop)
            stop_srs
            ;;
        restart)
            restart_srs
            ;;
        status)
            status_srs
            ;;
        logs)
            logs_srs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 无效的命令: $1"
            print_message $NC ""
            show_help
            exit 1
            ;;
    esac
}

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    print_message $YELLOW "⚠️  建议以root权限运行此脚本"
fi

# 执行主函数
main "$@"

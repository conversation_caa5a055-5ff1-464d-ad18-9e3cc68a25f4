# WebRTC推流快速参考

## 🚀 最小实现代码

### HTML (最简版本)
```html
<!DOCTYPE html>
<html>
<head>
    <title>WebRTC推流</title>
</head>
<body>
    <video id="localVideo" autoplay muted width="640" height="480"></video>
    <button id="startBtn">开始推流</button>
    <button id="stopBtn" disabled>停止推流</button>
    <div id="status">状态: 未连接</div>
    
    <script>
        // 在这里放入WebRTC推流代码
    </script>
</body>
</html>
```

### JavaScript (核心实现)
```javascript
class SimpleWebRTCPublisher {
    constructor() {
        this.pc = null;
        this.localStream = null;
        this.isPublishing = false;
        
        // 绑定元素
        this.localVideo = document.getElementById('localVideo');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.statusDiv = document.getElementById('status');
        
        // 绑定事件
        this.startBtn.onclick = () => this.start();
        this.stopBtn.onclick = () => this.stop();
    }
    
    async start() {
        try {
            this.updateStatus('正在启动...');
            
            // 1. 获取摄像头
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            });
            this.localVideo.srcObject = this.localStream;
            
            // 2. 创建WebRTC连接
            this.pc = new RTCPeerConnection({
                iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
            });
            
            // 3. 添加媒体轨道
            this.localStream.getTracks().forEach(track => {
                this.pc.addTrack(track, this.localStream);
            });
            
            // 4. 创建Offer
            const offer = await this.pc.createOffer();
            await this.pc.setLocalDescription(offer);
            
            // 5. 发送WHIP请求
            const response = await fetch('https://**************:1990/rtc/v1/whip/?app=live&stream=livestream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/sdp',
                    'Accept': 'application/sdp'
                },
                body: offer.sdp
            });
            
            if (!response.ok) {
                throw new Error(`WHIP请求失败: ${response.status}`);
            }
            
            // 6. 设置Answer
            const answerSdp = await response.text();
            await this.pc.setRemoteDescription(new RTCSessionDescription({
                type: 'answer',
                sdp: answerSdp
            }));
            
            this.isPublishing = true;
            this.updateStatus('推流成功！');
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            
        } catch (error) {
            console.error('推流失败:', error);
            this.updateStatus(`推流失败: ${error.message}`);
            this.stop();
        }
    }
    
    stop() {
        this.isPublishing = false;
        
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
            this.localVideo.srcObject = null;
        }
        
        if (this.pc) {
            this.pc.close();
            this.pc = null;
        }
        
        this.updateStatus('推流已停止');
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
    }
    
    updateStatus(message) {
        this.statusDiv.textContent = `状态: ${message}`;
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new SimpleWebRTCPublisher();
});
```

## 🔧 关键配置

### SRS服务器配置
- **WHIP URL**: `https://服务器IP:1990/rtc/v1/whip/?app=应用名&stream=流名`
- **默认应用**: `live`
- **默认流名**: `livestream`

### 播放地址
- **WebRTC**: `webrtc://服务器IP/live/livestream`
- **HLS**: `https://服务器IP:8088/live/livestream.m3u8`
- **HTTP-FLV**: `https://服务器IP:8088/live/livestream.flv`

## ⚠️ 常见问题

### 1. 摄像头权限被拒绝
```javascript
// 检查权限
if (error.name === 'NotAllowedError') {
    alert('请允许访问摄像头和麦克风');
}
```

### 2. WHIP请求失败
```javascript
// 检查网络连接
if (error.message.includes('Failed to fetch')) {
    alert('无法连接到SRS服务器，请检查服务器地址');
}
```

### 3. WebRTC连接失败
```javascript
// 监听连接状态
this.pc.onconnectionstatechange = () => {
    console.log('连接状态:', this.pc.connectionState);
    if (this.pc.connectionState === 'failed') {
        alert('WebRTC连接失败');
    }
};
```

## 📋 开发检查清单

- [ ] 确保使用HTTPS协议
- [ ] 检查SRS服务器运行状态
- [ ] 修改服务器IP地址
- [ ] 测试摄像头权限获取
- [ ] 验证WHIP请求成功
- [ ] 确认WebRTC连接建立
- [ ] 测试多种播放格式

## 🎯 快速测试

1. 复制上述代码到HTML文件
2. 修改服务器IP地址
3. 在HTTPS环境下打开页面
4. 点击"开始推流"
5. 允许摄像头权限
6. 查看状态显示
7. 使用播放地址测试播放

---

**提示**: 完整的实现请参考 `前端WebRTC推流接入指南.md`

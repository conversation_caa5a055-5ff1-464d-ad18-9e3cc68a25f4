# SRS 5.0 WebRTC服务器完整搭建指南

## 📋 概述

本文档详细介绍如何在Linux环境下搭建支持WebRTC推流的SRS (Simple Realtime Server) 5.0服务器，实现浏览器WebRTC推流、RTMP转发、HLS播放等功能。

## ⚡ 快速开始

如果您已有基础环境，可以直接执行以下命令快速部署：

```bash
# 1. 获取源码并编译
cd /data/threeServices/srs/trunk
./configure --rtc=on --ssl=on --ffmpeg-fit=on
make

# 2. 生成证书（修改IP为您的服务器IP）
cd conf && ./generate_cert.sh

# 3. 启动服务器
cd /data/threeServices
./srs_server_manager.sh start

# 4. 检查状态
./srs_server_manager.sh status

# 5. 测试推流
# 访问: https://您的服务器IP:8088/players/whip.html
```

**重要**: 请确保修改配置文件中的IP地址为您的实际服务器IP。

## 🎯 功能特性

- ✅ WebRTC推流（浏览器直接推流）
- ✅ RTMP推流和播放
- ✅ HLS (HTTP Live Streaming) 播放
- ✅ HTTP-FLV播放
- ✅ HTTPS支持（解决浏览器摄像头访问限制）
- ✅ 内网和公网部署支持
- ✅ 多种音视频编码格式

## 🛠️ 环境要求

### 系统要求
- **操作系统**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **CPU**: x86_64架构
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低10GB可用空间

### 依赖软件
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y git build-essential
sudo apt-get install -y openssl libssl-dev
sudo apt-get install -y curl wget

# CentOS/RHEL
sudo yum update
sudo yum groupinstall -y "Development Tools"
sudo yum install -y openssl openssl-devel
sudo yum install -y curl wget git
```

## 📥 第一步：获取SRS源码

```bash
# 创建工作目录
mkdir -p /data/threeServices
cd /data/threeServices

# 克隆SRS源码
git clone https://github.com/ossrs/srs.git
cd srs

# 切换到稳定版本
git checkout v5.0-r3

# 查看版本信息
git describe --tags
```

## 🔧 第二步：编译SRS

```bash
# 进入编译目录
cd trunk

# 配置编译选项（启用WebRTC和SSL支持）
./configure --rtc=on --ssl=on --ffmpeg-fit=on

# 开始编译（根据CPU核心数调整-j参数）
make -j$(nproc)

# 验证编译结果
ls -la objs/srs
```

### 编译选项说明
- `--rtc=on`: 启用WebRTC支持
- `--ssl=on`: 启用SSL/TLS支持
- `--ffmpeg-fit=on`: 启用FFmpeg支持（音视频转码）

## 🔐 第三步：生成HTTPS证书

```bash
# 进入配置目录
cd trunk/conf

# 生成证书（修改IP为您的实际服务器IP）
./generate_cert.sh
```

**重要**: 证书生成脚本已预配置，会自动生成 `server_compatible.key` 和 `server_compatible.crt` 文件。

## ⚙️ 第四步：配置SRS

生产环境配置文件已预配置为 `conf/webrtc_https_production.conf`。

**重要配置项**：
- **candidate**: 修改为您的实际服务器IP地址
- **证书文件**: 使用 `server_compatible.key` 和 `server_compatible.crt`
- **端口配置**: RTMP(1935), HTTP(8080), HTTPS(8088), API(1985/1990), WebRTC(8000)

## 🚀 第五步：启动SRS服务器

### 使用管理脚本（推荐）
```bash
# 返回项目根目录
cd /data/threeServices

# 启动服务器
./srs_server_manager.sh start

# 检查状态
./srs_server_manager.sh status

# 其他命令
./srs_server_manager.sh stop     # 停止服务器
./srs_server_manager.sh restart  # 重启服务器
./srs_server_manager.sh logs     # 查看日志
```

### 手动启动方法
```bash
cd trunk
./objs/srs -c conf/webrtc_https_production.conf
```

## 🌐 第六步：网络配置

### 防火墙配置
```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 1935/tcp    # RTMP
sudo ufw allow 8080/tcp    # HTTP
sudo ufw allow 8088/tcp    # HTTPS
sudo ufw allow 1985/tcp    # HTTP API
sudo ufw allow 1990/tcp    # HTTPS API
sudo ufw allow 8000/udp    # WebRTC

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=1935/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=8088/tcp
sudo firewall-cmd --permanent --add-port=1985/tcp
sudo firewall-cmd --permanent --add-port=1990/tcp
sudo firewall-cmd --permanent --add-port=8000/udp
sudo firewall-cmd --reload
```

### 端口说明
| 端口 | 协议 | 用途 |
|------|------|------|
| 1935 | TCP | RTMP推流和播放 |
| 8080 | TCP | HTTP Web服务 |
| 8088 | TCP | HTTPS Web服务 |
| 1985 | TCP | HTTP API接口 |
| 1990 | TCP | HTTPS API接口 |
| 8000 | UDP | WebRTC媒体传输 |

## 📱 第七步：测试WebRTC推流

### 访问地址
- **管理控制台**: `https://**************:8088/`
- **WebRTC推流**: `https://**************:8088/players/whip.html`
- **测试前端**: `/data/threeServices/web/webrtc-publisher.html`

### 推流测试
1. 打开推流页面，处理证书警告
2. 允许摄像头和麦克风权限
3. 点击"开始推流"
4. 查看推流统计信息

### 播放地址
- **WebRTC**: `webrtc://**************/live/livestream`
- **HLS**: `https://**************:8088/live/livestream.m3u8`
- **HTTP-FLV**: `https://**************:8088/live/livestream.flv`

## 🔧 第八步：系统服务配置（可选）

### 创建systemd服务
```bash
sudo cat > /etc/systemd/system/srs.service << 'EOF'
[Unit]
Description=SRS (Simple Realtime Server)
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/data/threeServices/srs/trunk
ExecStart=/data/threeServices/srs/trunk/objs/srs -c /data/threeServices/srs/trunk/conf/webrtc_https_production.conf
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable srs
sudo systemctl start srs
```

## 📊 第九步：监控和维护

### 使用管理脚本
```bash
# 检查服务状态
./srs_server_manager.sh status

# 查看实时日志
./srs_server_manager.sh logs

# 或查看完整日志
tail -f /data/threeServices/srs/trunk/srs.log
```

### 重要文件位置
- **配置文件**: `/data/threeServices/srs/trunk/conf/webrtc_https_production.conf`
- **日志文件**: `/data/threeServices/srs/trunk/srs.log`
- **管理脚本**: `/data/threeServices/srs_server_manager.sh`
- **测试前端**: `/data/threeServices/web/webrtc-publisher.html`

## ⚠️ 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查配置文件语法
   ./objs/srs -t -c conf/webrtc_https_production.conf

   # 查看详细错误
   ./srs_server_manager.sh logs
   ```

2. **WebRTC推流失败**
   ```bash
   # 检查candidate配置
   grep candidate conf/webrtc_https_production.conf

   # 检查UDP端口
   ss -ulpn | grep 8000
   ```

3. **HTTPS访问失败**
   ```bash
   # 检查证书文件
   ls -la conf/server_compatible.*

   # 测试HTTPS连接
   curl -k -v https://localhost:8088/
   ```

### 快速诊断
```bash
# 使用管理脚本检查状态
./srs_server_manager.sh status
```

## 🎯 性能优化

### 生产环境建议
```bash
# 修改配置文件中的以下参数
max_connections     5000;    # 增加最大连接数
daemon              on;      # 启用守护进程模式
srs_log_tank        file;    # 使用文件日志
```

### 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf
```

## 📚 扩展功能

### 录制功能
在配置文件中启用DVR录制：
```bash
dvr {
    enabled     on;
    dvr_path    ./objs/nginx/html/dvr;
    dvr_plan    session;
}
```


### 集群部署
使用Nginx进行负载均衡，配置多个SRS实例实现高可用。

## 🚀 快速部署检查清单

### 部署验证
- [ ] SRS服务器编译成功
- [ ] 证书生成完成
- [ ] 配置文件IP地址正确
- [ ] 服务启动正常
- [ ] 所有端口监听正常
- [ ] WebRTC推流测试成功
- [ ] 多种播放格式可用

## 📞 技术支持

### 官方资源
- **官方文档**: https://ossrs.net/
- **GitHub仓库**: https://github.com/ossrs/srs
- **社区论坛**: https://github.com/ossrs/srs/discussions
- **Docker镜像**: https://hub.docker.com/r/ossrs/srs

### 社区支持
- **QQ群**: 687936269
- **微信群**: 扫描官网二维码
- **Stack Overflow**: 标签 `simple-realtime-server`

### 商业支持
- **企业版**: https://ossrs.net/product/
- **技术咨询**: <EMAIL>
- **定制开发**: 联系官方团队

---

**版本信息**: SRS 5.0.213
**文档更新**: 2025-08-26
**适用环境**: Linux x86_64
**作者**: SRS技术团队
**许可证**: MIT License

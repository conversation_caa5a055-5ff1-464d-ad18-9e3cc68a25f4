# SRS WebRTC 推流服务器

基于SRS 5.0搭建的WebRTC推流服务器，支持浏览器直接推流和多种播放格式。

## 🎯 功能特性

- ✅ **WebRTC推流** - 浏览器直接推流，无需插件
- ✅ **多种播放格式** - WebRTC、HLS、HTTP-FLV
- ✅ **HTTPS支持** - 解决现代浏览器安全限制
- ✅ **内网部署** - 支持内网环境使用
- ✅ **实时监控** - 推流状态和性能监控
- ✅ **管理脚本** - 一键启停和状态检查

## 🚀 快速开始

### 1. 启动SRS服务器
```bash
# 启动服务器
./srs_server_manager.sh start

# 检查状态
./srs_server_manager.sh status
```

### 2. 测试WebRTC推流
```bash
# 方法1: 使用SRS内置页面
https://**************:8088/players/whip.html

# 方法2: 使用自定义测试页面
open web/webrtc-publisher.html
```

### 3. 播放测试
- **WebRTC播放**: `webrtc://**************/live/livestream`
- **HLS播放**: `https://**************:8088/live/livestream.m3u8`
- **HTTP-FLV播放**: `https://**************:8088/live/livestream.flv`

## 📁 项目结构

```
/data/threeServices/
├── srs/                           # SRS源码目录
│   └── trunk/
│       ├── conf/
│       │   ├── webrtc_https_production.conf  # 生产环境配置
│       │   ├── server_compatible.crt         # SSL证书
│       │   ├── server_compatible.key         # SSL私钥
│       │   └── generate_cert.sh              # 证书生成脚本
│       └── objs/srs                          # SRS可执行文件
├── web/                           # 测试前端
│   ├── webrtc-publisher.html      # WebRTC推流测试页面
│   ├── webrtc-publisher.js        # JavaScript逻辑
│   └── README.md                  # 前端使用说明
├── docs/                          # 文档目录
│   ├── SRS服务器搭建步骤.md        # 完整搭建指南
│   └── 前端WebRTC推流接入指南.md   # 前端开发指南
├── srs_server_manager.sh          # SRS服务器管理脚本
└── README.md                      # 项目说明（本文件）
```

## 🔧 管理命令

### SRS服务器管理
```bash
./srs_server_manager.sh start     # 启动服务器
./srs_server_manager.sh stop      # 停止服务器
./srs_server_manager.sh restart   # 重启服务器
./srs_server_manager.sh status    # 检查状态
./srs_server_manager.sh logs      # 查看日志
./srs_server_manager.sh help      # 显示帮助
```

### 服务状态检查
```bash
# 检查端口监听
ss -tulpn | grep -E "(1935|8080|8088|1985|1990|8000)"

# 测试HTTPS服务
curl -k https://**************:8088/

# 测试API服务
curl -s http://**************:1985/api/v1/versions
```

## 🌐 网络配置

### 端口说明
| 端口 | 协议 | 用途 |
|------|------|------|
| 1935 | TCP | RTMP推流和播放 |
| 8080 | TCP | HTTP Web服务 |
| 8088 | TCP | HTTPS Web服务 |
| 1985 | TCP | HTTP API接口 |
| 1990 | TCP | HTTPS API接口 |
| 8000 | UDP | WebRTC媒体传输 |

### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 1935/tcp 8080/tcp 8088/tcp 1985/tcp 1990/tcp 8000/udp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port={1935,8080,8088,1985,1990}/tcp
sudo firewall-cmd --permanent --add-port=8000/udp
sudo firewall-cmd --reload
```

## 📱 浏览器兼容性

| 浏览器 | 最低版本 | 推荐版本 | 备注 |
|--------|----------|----------|------|
| Chrome | 60+ | 最新版 | 最佳支持 |
| Firefox | 55+ | 最新版 | 良好支持 |
| Safari | 11+ | 最新版 | 需要HTTPS |
| Edge | 79+ | 最新版 | 基于Chromium |

## ⚠️ 注意事项

### HTTPS要求
- 现代浏览器要求HTTPS环境才能访问摄像头
- 自签名证书需要用户手动信任
- 生产环境建议使用正式SSL证书

### 网络要求
- 确保客户端能访问服务器的8088端口(HTTPS)
- 确保UDP 8000端口开放用于WebRTC媒体传输
- 内网环境需要正确配置candidate IP

### 性能建议
- **高质量推流**: 1280x720@30fps, 5Mbps+
- **标准推流**: 640x480@25fps, 2Mbps+
- **移动端推流**: 480x360@15fps, 1Mbps+

## 🔍 故障排除

### 常见问题
1. **服务启动失败**: 检查配置文件语法和端口占用
2. **WebRTC连接失败**: 检查candidate配置和UDP端口
3. **HTTPS访问失败**: 检查证书文件和HTTPS端口
4. **推流无画面**: 检查浏览器权限和网络连接

### 快速诊断
```bash
# 使用管理脚本检查状态
./srs_server_manager.sh status

# 查看详细日志
./srs_server_manager.sh logs
```

## 📚 文档

- [SRS服务器搭建步骤](docs/SRS服务器搭建步骤.md) - 完整的服务器搭建指南
- [前端WebRTC推流接入指南](docs/前端WebRTC推流接入指南.md) - 前端开发接入文档
- [Web测试页面说明](web/README.md) - 测试前端使用说明

## 📞 技术支持

### 官方资源
- **SRS官方文档**: https://ossrs.net/
- **GitHub仓库**: https://github.com/ossrs/srs
- **社区论坛**: https://github.com/ossrs/srs/discussions

### 项目信息
- **版本**: SRS 5.0.213
- **更新时间**: 2025-08-26
- **适用环境**: Linux x86_64
- **许可证**: MIT License

---

🎉 **现在您可以开始使用WebRTC推流功能了！**
